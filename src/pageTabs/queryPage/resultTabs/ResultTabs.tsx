import React, { useEffect, useMemo, useCallback } from 'react'
import { Tabs } from 'antd'
import Watermark from '@pansy/react-watermark'
import { Iconfont } from 'src/components'
import {
  ResultGrid,
  ResultGridPagination,
  ResultGridMongo,
  TSQLResultGrid,
} from './resultContentGrid'
import { ResultLogs } from './resultLogs'
import { ResultJson } from './resultJson'
import { useSelector, useDispatch } from 'src/hook'
import {
  setVisibleQueryTabs,
  setVisibleNodeDetail,
  setVisibleSdt,
} from '../queryPageSlice'
import { setActiveResultTabKey, removeResultTabPane } from './resultTabsSlice'
import styles from './index.module.scss'
import { resetExecuteLogs, resetErrorLogs } from 'src/store/extraSlice/logsSlice'
import ErrorMessage from './errorMessage'
import { useTranslation } from 'react-i18next'

// 导入 SVG 图标
import ExecuteDialogIcon from 'src/assets/resultTabsAssets/executeDialog.svg'
import ErrorIcon from 'src/assets/resultTabsAssets/error.svg'
import ResultsIcon from 'src/assets/resultTabsAssets/results.svg'
import ParseIcon from 'src/assets/resultTabsAssets/parse.svg'

const { TabPane } = Tabs

interface ResultTabsProps {
  /** resultTab 所属的 queryPane 的唯一标识 */
  queryTabKey: string
}

export const ResultTabs: React.FC<ResultTabsProps> = (props) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { queryTabKey } = props
  const {
    executeKeyListMap,
    explainKeyListMap,
    resultTabMap,
    activeResultKeyMap,
  } = useSelector((state) => state.resultTabs)
  const { visibleQueryTabs } = useSelector((state) => state.queryPage)
  // 使用 useMemo 缓存 watermarkEffect，避免每次重新创建
  const watermarkEffect = useMemo(() => ({}), [])

  // 使用 useMemo 缓存计算结果
  const executeKeyList = useMemo(() => executeKeyListMap[queryTabKey] || [], [executeKeyListMap, queryTabKey])
  const explainKeyList = useMemo(() => explainKeyListMap[queryTabKey] || [], [explainKeyListMap, queryTabKey])
  const executeTabs = useMemo(() => executeKeyList.map((key) => resultTabMap[key]), [executeKeyList, resultTabMap])
  const explainTabs = useMemo(() => explainKeyList.map((key) => resultTabMap[key]), [explainKeyList, resultTabMap])

  const { tabInfoMap } = useSelector((state) => state.queryTabs)
  const paneType = tabInfoMap[queryTabKey]?.paneType;

  //获取存储当前tab执行分页查询条数
  const resultPageSize = useMemo(() => {
    return  tabInfoMap[queryTabKey]?.resultPageSize || 100;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[tabInfoMap[queryTabKey]?.resultPageSize])

  useEffect(() => {
    //设置默认结果集scale
    if (executeTabs && executeTabs.length) {
      executeTabs.forEach((item) => {
        try {
          const scaleVal = getValidScale(item?.info?.scale);

          const keyEleWrapper = document.getElementById(`${item.key}gridWrapper`)?.getElementsByClassName('ag-root-wrapper')
          if (!keyEleWrapper) return

          const rootEle = keyEleWrapper && keyEleWrapper?.[0] as HTMLElement
          if (!rootEle) return
          
          // 设置缩放比例
          rootEle.style.cssText = `zoom:${scaleVal}%`
        } catch (e) {
          console.error(`Error setting scale for item key: ${item.key}`, e);
        }
      })
    }
  }, [queryTabKey, executeTabs])

  // 辅助函数：确保 scale 值合法并限制范围
  const getValidScale = (scale?: number): number => {
    const defaultScale = 100; // 默认值
    if (typeof scale !== 'number' || isNaN(scale)) {
      return defaultScale;
    }
    const minScale = 10; // 最小值
    const maxScale = 1000; // 最大值
    return Math.max(minScale, Math.min(maxScale, scale));
  }

  // 清理执行日志的处理函数，使用 useCallback 避免闭包问题
  const handleClearExecuteLog = useCallback(() => {
    dispatch(resetExecuteLogs(queryTabKey))
  }, [dispatch, queryTabKey])

  // 清理错误日志的处理函数，使用 useCallback 避免闭包问题
  const handleClearErrorLog = useCallback(() => {
    dispatch(resetErrorLogs(queryTabKey))
  }, [dispatch, queryTabKey])

  // 全屏显示处理函数，使用 useCallback 避免闭包问题和内存泄露
  const handleFullscreenToggle = useCallback(() => {
    dispatch(setVisibleQueryTabs(false))
    dispatch(setVisibleNodeDetail(false))
    dispatch(setVisibleSdt(false))
  }, [dispatch])

  // 退出全屏处理函数，使用 useCallback 避免闭包问题和内存泄露
  const handleExitFullscreen = useCallback(() => {
    dispatch(setVisibleSdt(true))
    dispatch(setVisibleQueryTabs(true))
  }, [dispatch])

  // 结果标签页切换处理函数，使用 useCallback 避免闭包问题和内存泄露
  const handleResultTabChange = useCallback((activeKey: string) => {
    dispatch(
      setActiveResultTabKey({ queryTabKey, resultTabKey: activeKey }),
    )
  }, [dispatch, queryTabKey])

  const onEditResultPane = (targetKey: React.MouseEvent | string, action: 'add' | 'remove') => {
    if (action !== 'remove') return
    //@ts-ignore
    dispatch(removeResultTabPane({ targetResultKey: targetKey, queryTabKey: queryTabKey }))
  }

  const extraOperations = (
    <div className={styles.extraOperations}>
      {visibleQueryTabs ? (
        <Iconfont
          className={styles.extraOperationIcon}
          type="icon-fullscreen"
          onClick={handleFullscreenToggle}
        />
      ) : (
        <Iconfont
          className={styles.extraOperationIcon}
          type="icon-fullscreen-exit"
          onClick={handleExitFullscreen}
        />
      )}
    </div>
  )

  // watermark - 使用 useMemo 缓存，只在相关字段变化时重新创建
  const { watermarkSetting, watermarkValue } = useSelector(
    (state) => ({
      watermarkSetting: state.login.userInfo?.watermarkSetting,
      watermarkValue: state.login.userInfo?.watermarkValue
    }),
    // 使用浅比较，只有这两个字段变化时才重新渲染
    (left, right) =>
      left.watermarkSetting === right.watermarkSetting &&
      JSON.stringify(left.watermarkValue) === JSON.stringify(right.watermarkValue)
  )

  const watermark = useMemo(() => (
    <Watermark
      text={watermarkValue?.length ? watermarkValue?.join(' ') : ''}
      pack={false}
      visible={watermarkSetting}
      rotate={20}
      zIndex={99}
      {...watermarkEffect}
    />
  ), [watermarkSetting, watermarkValue, watermarkEffect])
  
  const renderTabPanes = useMemo(() => {
    return executeTabs
      .map(({ key, info }, index) => {
        const { dataSourceType, executeError } = info || {};
        if (dataSourceType === 'MongoDB') {
          return [
            <TabPane
              closable={false}
              className={styles.resultPaneWrapper}
              key={key}
              tab={`JSON ${t('sdo_view_supper')}(${index + 1})`}
            >
              {watermark}
              <ResultJson result={info} />
            </TabPane>,
            <TabPane
              closable={false}
              className={styles.resultPaneWrapper}
              key={`${key}-GRID`}
              tab={`${t('sdo_table_view')}(${index + 1})`}
            >
              {watermark}
              {!executeError ? (
                <ResultGridMongo result={info} queryKey={queryTabKey} tabResultKey={resultPageSize} />
              ) : (
                <div className={styles.resultErrorMessage}>
                  <pre>{executeError.message}</pre>
                </div>
              )}
            </TabPane>,
          ];
        }
  
        if (executeError?.message) {
          return null;
        }
  
        return (
          <TabPane
            className={styles.resultPaneWrapper}
            key={key}
            closable={true}
            tab={
              <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <img src={ResultsIcon} alt="resultsIcon" style={{ width: '16px', height: '16px' }} />
                {key.startsWith('cursor/')
                  ? (() => {
                      const parts = key.split('-');
                      // 从后往前取：最后一个是时间戳，倒数第二个是行号，倒数第三个是字段名
                      const rowIndex = parts[parts.length - 2] || '1';
                      const fieldName = parts[parts.length - 3] || 'Unknown';
                      return `${fieldName}(${rowIndex})`;
                    })()
                  : `${t("sdo_results")}（${index + 1}）`
                }
              </span>
            }
          >
            {watermark}
            {!executeError ? (
              paneType !== 'tSql' ? tabInfoMap[queryTabKey]?.isScrollLoading ?  
                <ResultGrid tabResultKey={key} result={info} queryKey={queryTabKey} rowNum={resultPageSize}  />
              :
               <ResultGridPagination tabResultKey={key} result={info} queryKey={queryTabKey} rowNum={resultPageSize} />
              : (
                <TSQLResultGrid tabResultKey={key} result={info} queryKey={queryTabKey} />
              )
            ) : (
              <div className={styles.resultErrorMessage}>
                <pre>{executeError.message}</pre>
              </div>
            )}
          </TabPane>
        );
      })
      .flat() // 展平数组，处理 MongoDB 返回的多个 TabPane
      .filter(Boolean); // 过滤掉无效的 null 值
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [executeTabs, paneType, queryTabKey, tabInfoMap[queryTabKey], t]);

  const memoizedErrorMessage = useMemo(() => {
    return <ErrorMessage queryKey={queryTabKey} />
  }, [queryTabKey]);

  return (
    <Tabs
      defaultActiveKey={`log/${queryTabKey}`}
      activeKey={activeResultKeyMap[queryTabKey]}
      onChange={handleResultTabChange}
      type="editable-card"
      hideAdd
      //@ts-ignore
      onEdit={onEditResultPane}
      className={styles.resultTab}
      tabBarGutter={0}
      tabBarStyle={{ marginBottom: 0 }}
      tabBarExtraContent={extraOperations}
      animated={{ inkBar: false, tabPane: false }}
    >
       {/* 执行日志 Tab */} <TabPane
        closable={false}
        className={styles.resultPaneWrapper}
        key={`log/${queryTabKey}`}
        tab={
          <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <img src={ExecuteDialogIcon} alt="executeDialogIcon" style={{ width: '16px', height: '16px' }} />
            {t('sdo_execute_log')}
          </span>
        }
      >
        <div className={styles.clearLog}>
          <div className='flexAlignCenterJustifyEnd'>
            <span
              className='options'
              onClick={handleClearExecuteLog}
            >
              {t('sdo_clear_log')}
            </span>
          </div>
        </div>
        <ResultLogs queryKey={queryTabKey} activeResultTabKey={activeResultKeyMap[queryTabKey]}/>
      </TabPane>
      
      {/* 异常日志Tab */}
      <TabPane
        closable={false}
        className={styles.resultPaneWrapper}
        key={`errorLog/${queryTabKey}`}
        tab={
          <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <img src={ErrorIcon} alt="errorIcon" style={{ width: '16px', height: '16px' }} />
            {t('sdo_exception_log')}
          </span>
        }
      >
        <div className={styles.clearLog}>
          <div className='flexAlignCenterJustifyEnd'>
            <span
              className='options'
              onClick={handleClearErrorLog}
            >
              {t('sdo_clear_log')}
            </span>
          </div>
        </div>
        {memoizedErrorMessage}
      </TabPane>

      {/* 执行结果 Tab */}
      {renderTabPanes}

      {/* 解释 Tab */}
      {explainTabs.map(({ key, info }, index) => {
        const { executeError } = info
        return (
          <TabPane
            className={styles.resultPaneWrapper}
            key={key}
            closable={true}
            tab={
              <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <img src={ParseIcon} alt="parse" style={{ width: '16px', height: '16px' }} />
                {`${t('sdo_explanation')}（${index + 1}）`}
              </span>
            }
          >
            {watermark}
            {!executeError ? tabInfoMap[queryTabKey]?.isScrollLoading ?
              <ResultGrid result={info} queryKey={queryTabKey} type="explain" rowNum={resultPageSize} />
            :
             <ResultGridPagination result={info} queryKey={queryTabKey} type="explain" rowNum={resultPageSize}/>
            : (
              <div className={styles.resultErrorMessage}>
                <pre>{executeError.message}</pre>
              </div>
            )}
          </TabPane>
        )
      })}
    </Tabs>
  )
}
